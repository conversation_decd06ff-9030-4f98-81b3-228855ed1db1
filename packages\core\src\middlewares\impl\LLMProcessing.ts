import { Context } from "koishi";
import { CoreProcessingMiddleware, middleware, validateConfig } from "../core/BaseMiddleware";
import { ConversationState, MiddlewareContext, MiddlewarePriority } from "../core/MiddlewareCore";

import { ChatModel } from "../../adapters/impl/ChatModel";
import { LLMRequestError, LLMRetryExhaustedError, LLMTimeoutError } from "../../shared/errors/llm.error";

/**
 * 重试配置
 */
export interface RetryConfig {
    MaxRetries: number;
    TimeoutMs: number;
    RetryDelayMs: number;
    ExponentialBackoff: boolean;
    RetryableErrors: string[];
}

/**
 * 适配器切换配置
 */
export interface AdapterSwitchingConfig {
    Enabled: boolean;
    MaxAttempts: number;
}

/**
 * LLM处理中间件配置
 */
export interface LLMProcessingConfig {
    Debug?: boolean;
    RetryConfig: RetryConfig;
    AdapterSwitchingConfig: AdapterSwitchingConfig;
    Timeout?: number;
    EnableStreaming?: boolean;
}

/**
 * LLM重试管理器
 */
class RetryManager {
    constructor(private config: LLMProcessingConfig["RetryConfig"], private logger: any) {}

    async executeWithRetry<T>(
        operation: (abortSignal: AbortSignal, cancelTimeout: () => void) => Promise<T>,
        adapterName: string
    ): Promise<T> {
        let lastError: Error | null = null;

        for (let attempt = 0; attempt <= this.config.MaxRetries; attempt++) {
            try {
                // 创建超时控制器
                const controller = new AbortController();
                const timeoutId = setTimeout(() => {
                    controller.abort();
                }, this.config.TimeoutMs);

                const cancelTimeout = () => clearTimeout(timeoutId);

                const result = await operation(controller.signal, cancelTimeout);
                clearTimeout(timeoutId);

                if (attempt > 0) {
                    this.logger.info(`重试成功 (适配器: ${adapterName}, 尝试: ${attempt + 1})`);
                }

                return result;
            } catch (error) {
                lastError = error as Error;

                // 检查是否为可重试错误
                if (!this.isRetryableError(error as Error)) {
                    throw error;
                }

                // 最后一次尝试失败
                if (attempt === this.config.MaxRetries) {
                    break;
                }

                // 计算延迟时间
                const delay = this.calculateDelay(attempt);
                this.logger.warn(
                    `LLM请求失败，${delay}ms后重试 (适配器: ${adapterName}, ` +
                        `尝试: ${attempt + 1}/${this.config.MaxRetries + 1}, 错误: ${error.message})`
                );

                await this.sleep(delay);
            }
        }

        throw new LLMRetryExhaustedError(`LLM请求重试耗尽 (适配器: ${adapterName})`, this.config.MaxRetries, null, lastError!);
    }

    private isRetryableError(error: Error): boolean {
        return this.config.RetryableErrors.some((pattern) => error.name.includes(pattern) || error.message.includes(pattern));
    }

    private calculateDelay(attempt: number): number {
        if (this.config.ExponentialBackoff) {
            return this.config.RetryDelayMs * Math.pow(2, attempt);
        }
        return this.config.RetryDelayMs;
    }

    private sleep(ms: number): Promise<void> {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
}

/**
 * LLM适配器管理器
 */
class AdapterManager {
    constructor(private chatModelSwitcher: any, private config: LLMProcessingConfig["AdapterSwitchingConfig"], private logger: any) {}

    async executeWithAdapterSwitching<T>(operation: (adapterName: string, model: ChatModel) => Promise<T>): Promise<T> {
        if (!this.config.Enabled) {
            const model = this.chatModelSwitcher.getCurrentModel();
            const adapterName = this.chatModelSwitcher.getCurrentAdapterName();
            return await operation(adapterName, model);
        }

        const availableAdapters = this.chatModelSwitcher.getAvailableAdapters();
        let lastError: Error | null = null;

        for (let attempt = 0; attempt < Math.min(this.config.MaxAttempts, availableAdapters.length); attempt++) {
            try {
                const adapterName = availableAdapters[attempt];
                const model = this.chatModelSwitcher.switchToAdapter(adapterName);

                this.logger.debug(`尝试适配器: ${adapterName} (尝试: ${attempt + 1})`);

                const result = await operation(adapterName, model);

                if (attempt > 0) {
                    this.logger.info(`适配器切换成功: ${adapterName}`);
                }

                return result;
            } catch (error) {
                lastError = error as Error;
                this.logger.warn(`适配器 ${availableAdapters[attempt]} 失败: ${error.message}`);

                // 如果是最后一次尝试，抛出错误
                if (attempt === this.config.MaxAttempts - 1 || attempt === availableAdapters.length - 1) {
                    break;
                }
            }
        }

        throw lastError || new Error("所有适配器都失败了");
    }
}

/**
 * LLM处理中间件
 * 职责：处理LLM调用，包括重试机制和适配器切换
 */
@middleware({
    id: "builtin.llm-processing",
    name: "LLM处理中间件",
    phase: "core_processing" as any,
    priority: MiddlewarePriority.HIGHEST,
    dependencies: ["builtin.reply-condition"],
})
@validateConfig<LLMProcessingConfig>((config) => {
    if (config.RetryConfig.MaxRetries < 0 || config.RetryConfig.MaxRetries > 10) {
        return "RetryConfig.MaxRetries 必须在 0-10 之间";
    }
    if (config.RetryConfig.TimeoutMs < 1000) {
        return "RetryConfig.TimeoutMs 不能小于 1000ms";
    }
    return true;
})
export class LLMProcessingMiddleware extends CoreProcessingMiddleware<LLMProcessingConfig> {
    private retryManager: RetryManager;
    private adapterManager: AdapterManager;

    constructor(ctx: Context, config: LLMProcessingConfig) {
        super(ctx, config);

        this.retryManager = new RetryManager(config.RetryConfig, this.logger);

        // 获取聊天模型切换器
        const chatModelSwitcher = this.ctx["yesimbot.model"]?.getChatModelSwitcher();
        this.adapterManager = new AdapterManager(chatModelSwitcher, config.AdapterSwitchingConfig, this.logger);
    }

    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        // 只在处理状态下执行
        if (ctx.state !== ConversationState.PROCESSING) {
            await next();
            return;
        }

        const startTime = Date.now();

        try {
            // 1. 构建提示词
            const prompts = await this.buildPrompts(ctx);

            // 2. 执行LLM请求
            const response = await this.executeLLMRequest(ctx, prompts);

            // 3. 设置响应数据
            ctx.llmResponse = response;
            this.setShared(ctx, "llmResponse", response);

            // 4. 转换状态
            await ctx.transitionTo(ConversationState.RESPONDING);

            await next();
        } catch (error) {
            await this.handleLLMError(error as Error, ctx);
            throw error;
        } finally {
            this.recordMetric(ctx, "execution_time", Date.now() - startTime);
        }
    }

    /**
     * 构建提示词
     */
    private async buildPrompts(ctx: MiddlewareContext): Promise<{ system: string; user: string }> {
        try {
            const promptBuilder = this.ctx["yesimbot.promptBuilder"];
            if (!promptBuilder) {
                throw new Error("提示词构建器未找到");
            }

            const system = await promptBuilder.buildSystemPrompt(ctx);
            const user = await promptBuilder.buildUserPrompt(ctx);

            this.recordMetric(ctx, "prompt_build_time", Date.now());

            return { system, user };
        } catch (error) {
            this.logger.error("构建提示词失败:", error);
            throw new LLMRequestError("提示词构建失败", null, null, null, error as Error);
        }
    }

    /**
     * 执行LLM请求
     */
    private async executeLLMRequest(ctx: MiddlewareContext, prompts: { system: string; user: string }): Promise<any> {
        const requestStartTime = Date.now();

        try {
            const response = await this.adapterManager.executeWithAdapterSwitching(async (adapterName: string, model: ChatModel) => {
                return await this.retryManager.executeWithRetry(async (abortSignal: AbortSignal, cancelTimeout: () => void) => {
                    return await model.chat(
                        [
                            { role: "system", content: prompts.system },
                            { role: "user", content: prompts.user },
                        ],
                        {
                            debug: this.config.Debug,
                            logger: this.logger,
                            abortSignal,
                            onStreamStart: cancelTimeout,
                        }
                    );
                }, adapterName);
            });

            const duration = Date.now() - requestStartTime;
            this.recordMetric(ctx, "llm_request_time", duration);
            this.logger.debug(`LLM请求完成，耗时: ${duration}ms`);

            return response;
        } catch (error) {
            const duration = Date.now() - requestStartTime;
            this.recordMetric(ctx, "llm_request_time", duration);
            throw error;
        }
    }

    /**
     * 处理LLM错误
     */
    private async handleLLMError(error: Error, ctx: MiddlewareContext): Promise<void> {
        if (error instanceof LLMRetryExhaustedError) {
            this.logger.error(`LLM重试耗尽: ${error.message}`);
            this.setShared(ctx, "llmError", {
                type: "retry_exhausted",
                message: error.message,
                // attempts: error.attempts,
            });
        } else if (error instanceof LLMTimeoutError) {
            this.logger.warn(`LLM请求超时: ${error.message}`);
            this.setShared(ctx, "llmError", {
                type: "timeout",
                message: error.message,
            });
        } else if (error instanceof LLMRequestError) {
            this.logger.warn(`LLM请求错误: ${error.message}`);
            this.setShared(ctx, "llmError", {
                type: "request_error",
                message: error.message,
            });
        } else if (error.name === "AbortError") {
            this.logger.info("LLM请求被中止");
            this.setShared(ctx, "llmError", {
                type: "aborted",
                message: "请求被中止",
            });
        } else {
            this.logger.error(`未预期的LLM错误: ${error.message}`);
            this.setShared(ctx, "llmError", {
                type: "unknown",
                message: error.message,
            });
        }
    }

    /**
     * 健康检查
     */
    async healthCheck(): Promise<boolean> {
        try {
            const chatModelSwitcher = this.ctx["yesimbot.model"]?.getChatModelSwitcher();
            return !!chatModelSwitcher && chatModelSwitcher.isHealthy();
        } catch (error) {
            this.logger.error("LLM健康检查失败:", error);
            return false;
        }
    }
}
