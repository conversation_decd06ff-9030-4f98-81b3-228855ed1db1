import { Context } from "koishi";
import { ConditionCheckMiddleware, middleware, validateConfig } from "../core/BaseMiddleware";
import { ConversationState, MiddlewareContext, MiddlewarePriority } from "../core/MiddlewareCore";

/**
 * 回复条件中间件配置
 */
export interface ReplyConditionConfig {
    Channels: string[][];
    TestMode?: boolean;
    Strategies: {
        AtMention: {
            Enabled: boolean;
            Probability: number;
        };
        Threshold: {
            Enabled: boolean;
            Value: number;
        };
        ConversationFlow: {
            Enabled: boolean;
            ConfidenceThreshold: number;
        };
    };
    Timing: {
        WaitTime: number;
        SameUserThreshold: number;
    };
    Advanced?: {
        Willingness?: {
            MessageIncrease: number;
            AtIncrease: number;
            DecayRate: number;
            RetentionAfterReply: number;
            Keywords?: {
                List: string[];
                Increase: number;
            };
        };
    };
}

/**
 * 意愿值管理器
 */
class WillingnessManager {
    private willingnessMap = new Map<string, number>();
    private lastDecayTime = Date.now();

    constructor(private config: ReplyConditionConfig["Advanced"]) {}

    /**
     * 增加意愿值
     */
    increaseWillingness(channelId: string, amount: number): void {
        const current = this.willingnessMap.get(channelId) || 0;
        this.willingnessMap.set(channelId, Math.min(100, current + amount));
    }

    /**
     * 获取当前意愿值
     */
    getWillingness(channelId: string): number {
        this.decayWillingness();
        return this.willingnessMap.get(channelId) || 0;
    }

    /**
     * 回复后保留意愿值
     */
    retainAfterReply(channelId: string): void {
        const current = this.willingnessMap.get(channelId) || 0;
        const retention = this.config?.Willingness?.RetentionAfterReply || 0.3;
        this.willingnessMap.set(channelId, current * retention);
    }

    /**
     * 意愿值衰减
     */
    private decayWillingness(): void {
        const now = Date.now();
        const elapsed = now - this.lastDecayTime;
        const minutes = elapsed / (60 * 1000);

        if (minutes >= 1) {
            const decayRate = this.config?.Willingness?.DecayRate || 2;

            for (const [channelId, willingness] of this.willingnessMap) {
                const newWillingness = Math.max(0, willingness - decayRate * minutes);
                this.willingnessMap.set(channelId, newWillingness);
            }

            this.lastDecayTime = now;
        }
    }
}

/**
 * 高级回复条件中间件
 * 职责：检查是否需要回复消息，支持多种回复策略
 */
@middleware({
    id: "builtin.reply-condition",
    name: "回复条件中间件",
    phase: "condition_check" as any,
    priority: MiddlewarePriority.HIGH,
    dependencies: ["builtin.database-storage"],
})
@validateConfig<ReplyConditionConfig>((config) => {
    if (config.Strategies.AtMention.Probability < 0 || config.Strategies.AtMention.Probability > 1) {
        return "atMention.probability 必须在 0-1 之间";
    }
    if (config.Strategies.Threshold.Value < 0 || config.Strategies.Threshold.Value > 1) {
        return "threshold.value 必须在 0-1 之间";
    }
    return true;
})
export class ReplyConditionMiddleware extends ConditionCheckMiddleware<ReplyConditionConfig> {
    private willingnessManager: WillingnessManager;
    private lastMessageTimes = new Map<string, number>();

    constructor(ctx: Context, config: ReplyConditionConfig) {
        super(ctx, config);
        this.willingnessManager = new WillingnessManager(config.Advanced);
    }

    async execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void> {
        const startTime = Date.now();

        try {
            // 1. 基础频道检查
            if (!this.checkChannelPermission(ctx)) {
                this.logger.debug(`频道 ${ctx.koishiSession.channelId} 不在允许列表中`);
                ctx.skip("频道不在允许列表中");
                return;
            }

            // 2. 测试模式检查
            if (this.config.TestMode) {
                this.logger.debug("测试模式：强制回复");
                await this.transitionToProcessing(ctx);
                await next();
                return;
            }

            // 3. 时间控制检查
            if (!this.checkTiming(ctx)) {
                this.logger.debug("时间控制：跳过回复");
                ctx.skip("时间控制限制");
                return;
            }

            // 4. 回复策略检查
            const shouldReply = await this.evaluateReplyStrategies(ctx);

            if (shouldReply) {
                await this.transitionToProcessing(ctx);
                await next();

                // 回复后处理意愿值
                this.willingnessManager.retainAfterReply(ctx.koishiSession.channelId);
            } else {
                this.logger.debug("回复策略：不满足回复条件");
                ctx.skip("不满足回复条件");
            }
        } finally {
            this.recordMetric(ctx, "execution_time", Date.now() - startTime);
        }
    }

    /**
     * 检查频道权限
     */
    private checkChannelPermission(ctx: MiddlewareContext): boolean {
        const channelId = ctx.koishiSession.channelId;
        return this.config.Channels.some((slots) => slots.includes(channelId));
    }

    /**
     * 检查时间控制
     */
    private checkTiming(ctx: MiddlewareContext): boolean {
        const now = Date.now();
        const channelId = ctx.koishiSession.channelId;
        const userId = ctx.koishiSession.userId;
        const userChannelKey = `${userId}:${channelId}`;

        // 检查等待时间
        const lastMessageTime = this.lastMessageTimes.get(channelId) || 0;
        if (now - lastMessageTime < this.config.Timing.WaitTime) {
            return false;
        }

        // 检查同用户阈值
        const lastUserMessageTime = this.lastMessageTimes.get(userChannelKey) || 0;
        if (now - lastUserMessageTime < this.config.Timing.SameUserThreshold) {
            return false;
        }

        // 更新时间记录
        this.lastMessageTimes.set(channelId, now);
        this.lastMessageTimes.set(userChannelKey, now);

        return true;
    }

    /**
     * 评估回复策略
     */
    private async evaluateReplyStrategies(ctx: MiddlewareContext): Promise<boolean> {
        const strategies = this.config.Strategies;
        let shouldReply = false;

        // 1. @提及策略
        if (strategies.AtMention.Enabled && ctx.isMentioned) {
            const probability = strategies.AtMention.Probability;
            if (Math.random() < probability) {
                this.logger.debug(`@提及策略：触发回复 (概率: ${probability})`);
                shouldReply = true;
            }

            // 增加意愿值
            const increase = this.config.Advanced?.Willingness?.AtIncrease || 30;
            this.willingnessManager.increaseWillingness(ctx.koishiSession.channelId, increase);
        }

        // 2. 阈值策略
        if (!shouldReply && strategies.Threshold.Enabled) {
            const willingness = this.willingnessManager.getWillingness(ctx.koishiSession.channelId);
            const threshold = strategies.Threshold.Value * 100; // 转换为百分比

            if (willingness >= threshold) {
                this.logger.debug(`阈值策略：触发回复 (意愿值: ${willingness}, 阈值: ${threshold})`);
                shouldReply = true;
            }
        }

        // 3. 对话流策略
        if (!shouldReply && strategies.ConversationFlow.Enabled) {
            const confidence = await this.analyzeConversationFlow(ctx);
            if (confidence >= strategies.ConversationFlow.ConfidenceThreshold) {
                this.logger.debug(`对话流策略：触发回复 (置信度: ${confidence})`);
                shouldReply = true;
            }
        }

        // 4. 关键词策略
        if (!shouldReply) {
            shouldReply = this.checkKeywords(ctx);
        }

        // 增加基础意愿值
        if (!ctx.isMentioned) {
            const increase = this.config.Advanced?.Willingness?.MessageIncrease || 10;
            this.willingnessManager.increaseWillingness(ctx.koishiSession.channelId, increase);
        }

        return shouldReply;
    }

    /**
     * 分析对话流
     */
    private async analyzeConversationFlow(ctx: MiddlewareContext): Promise<number> {
        try {
            // 这里可以实现更复杂的对话流分析逻辑
            // 例如：分析最近的消息历史、用户行为模式等

            // 简单实现：基于消息内容和时间间隔
            const content = ctx.koishiSession.content;
            const hasQuestion = /[？?]/.test(content);
            const hasKeywords = this.checkKeywords(ctx);

            let confidence = 0;
            if (hasQuestion) confidence += 0.3;
            if (hasKeywords) confidence += 0.4;
            if (content.length > 10) confidence += 0.2;

            return Math.min(1, confidence);
        } catch (error) {
            this.logger.error("分析对话流时发生错误:", error);
            return 0;
        }
    }

    /**
     * 检查关键词
     */
    private checkKeywords(ctx: MiddlewareContext): boolean {
        const keywords = this.config.Advanced?.Willingness?.Keywords;
        if (!keywords || !keywords.List.length) return false;

        const content = ctx.koishiSession.content.toLowerCase();
        const hasKeyword = keywords.List.some((keyword) => content.includes(keyword.toLowerCase()));

        if (hasKeyword) {
            const increase = keywords.Increase || 10;
            this.willingnessManager.increaseWillingness(ctx.koishiSession.channelId, increase);
            this.logger.debug(`关键词匹配：增加意愿值 ${increase}`);
            return true;
        }

        return false;
    }

    /**
     * 转换到处理状态
     */
    private async transitionToProcessing(ctx: MiddlewareContext): Promise<void> {
        await ctx.transitionTo(ConversationState.PROCESSING);
        this.setShared(ctx, "shouldReply", true);
        this.setShared(ctx, "replyReason", "条件检查通过");
    }

    /**
     * 获取意愿值统计
     */
    getWillingnessStats(): Record<string, number> {
        const stats: Record<string, number> = {};
        for (const [channelId, willingness] of this.willingnessManager["willingnessMap"]) {
            stats[channelId] = willingness;
        }
        return stats;
    }

    /**
     * 健康检查
     */
    async healthCheck(): Promise<boolean> {
        // 检查配置是否有效
        return this.config.Channels.length > 0;
    }
}
