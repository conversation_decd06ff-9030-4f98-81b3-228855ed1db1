import { Context, Session } from "koishi";
import { GenerateTextResult } from "xsai";
import { DefaultPlatform, OneBotPlatform, PlatformAdapter } from "../../services";
import { AgentResponse } from "../../services/worldstate/interfaces";

/**
 * 中间件执行阶段
 */
export enum MiddlewarePhase {
    /** 预处理阶段 - 错误处理、日志记录等 */
    PREPROCESSING = "preprocessing",
    /** 输入处理阶段 - 消息解析、存储等 */
    INPUT_PROCESSING = "input_processing",
    /** 条件检查阶段 - 回复条件判断等 */
    CONDITION_CHECK = "condition_check",
    /** 核心处理阶段 - LLM调用、推理等 */
    CORE_PROCESSING = "core_processing",
    /** 输出处理阶段 - 响应生成、工具调用等 */
    OUTPUT_PROCESSING = "output_processing",
    /** 后处理阶段 - 清理、统计等 */
    POSTPROCESSING = "postprocessing",
}

/**
 * 中间件优先级
 */
// export enum MiddlewarePriority {
//     HIGHEST = 1000,
//     HIGH = 800,
//     NORMAL = 500,
//     LOW = 200,
//     LOWEST = 100,
// }

export type MiddlewarePriority = number;

export const MiddlewarePriority = {
    HIGHEST: 1000 as MiddlewarePriority,
    HIGH: 800 as MiddlewarePriority,
    NORMAL: 500 as MiddlewarePriority,
    LOW: 200 as MiddlewarePriority,
    LOWEST: 100 as MiddlewarePriority,
} as const;

/**
 * 中间件状态
 */
export enum MiddlewareStatus {
    PENDING = "pending",
    RUNNING = "running",
    COMPLETED = "completed",
    FAILED = "failed",
    SKIPPED = "skipped",
}

/**
 * 执行状态
 */
export enum ExecutionState {
    IDLE = "idle",
    RUNNING = "running",
    PAUSED = "paused",
    COMPLETED = "completed",
    FAILED = "failed",
    CANCELLED = "cancelled",
}

/**
 * 响应类型
 */
export enum ResponseType {
    THINKING = "thinking", // 思考过程
    INTERMEDIATE = "intermediate", // 中间结果
    FINAL = "final", // 最终答案
    ERROR = "error", // 错误信息
}

/**
 * 响应项
 */
export interface ResponseItem {
    id: string;
    type: ResponseType;
    content: string;
    timestamp: Date;
    metadata?: Record<string, any>;
}

/**
 * 执行断点
 */
export interface ExecutionBreakpoint {
    phase: MiddlewarePhase;
    middlewareId?: string;
    condition?: (ctx: MiddlewareContext) => boolean;
    action?: "pause" | "skip" | "jump";
    target?: {
        phase: MiddlewarePhase;
        middlewareId?: string;
    };
}

/**
 * 会话状态枚举
 * 简化为三个核心状态
 */
export enum ConversationState {
    IDLE, // 空闲状态，等待新消息触发
    PROCESSING, // 处理中状态
    RESPONDING, // 响应中状态
}

/**
 * 消息上下文
 * 在中间件链中传递的上下文对象
 */
export class MessageContext {
    // 当前会话状态
    public state: ConversationState = ConversationState.IDLE;

    // LLM响应和处理后的响应
    public llmResponse?: GenerateTextResult;
    public processedResponse?: string[];

    public isMentioned: boolean = false;

    // heartbeat触发次数计数器
    public heartbeatCount: number = 0;

    public currentTurnId: string;
    public agentResponses: AgentResponse[] = [];

    public platform: PlatformAdapter;

    private constructor(
        // Koishi上下文对象
        public koishiContext: Context,
        // Koishi会话对象
        public koishiSession: Session,

        public allowedChannels: string[]
    ) {
        this.isMentioned = koishiSession.stripped.atSelf;
        let platformAdapter: PlatformAdapter;
        if (koishiSession.platform === "onebot") {
            platformAdapter = new OneBotPlatform(koishiSession);
        } else {
            platformAdapter = new DefaultPlatform(koishiSession);
        }
        this.platform = platformAdapter;
    }

    // 提供一个公共的、异步的创建方法
    public static async create(koishiContext: Context, koishiSession: Session, allowedChannels: string[]): Promise<MessageContext> {
        const messageContext = new MessageContext(koishiContext, koishiSession, allowedChannels);
        await messageContext.initializeTurn(); // 等待异步操作完成
        return messageContext;
    }

    private async initializeTurn(): Promise<void> {
        const turn = await this.koishiContext["yesimbot.data"].getLastTurn(this.koishiSession.platform, this.koishiSession.channelId);
        if (turn) {
            this.currentTurnId = turn.id;
        } else {
            const newTurn = await this.koishiContext["yesimbot.data"].startNewTurn(
                this.koishiSession.platform,
                this.koishiSession.channelId
            );
            this.currentTurnId = newTurn.id;
            this.koishiContext.logger.info(`[Turn] Started new turn: ${this.currentTurnId}`);
        }
    }

    /**
     * 转换会话状态
     */
    async transitionTo(newState: ConversationState): Promise<void> {
        this.state = newState;
    }
}

/**
 * 增强的中间件上下文
 */
export interface MiddlewareContext extends MessageContext {
    /** 中间件执行元数据 */
    readonly metadata: {
        /** 当前执行阶段 */
        currentPhase: MiddlewarePhase;
        /** 执行开始时间 */
        startTime: number;
        /** 已执行的中间件列表 */
        executedMiddlewares: string[];
        /** 中间件执行状态 */
        middlewareStates: Map<string, MiddlewareStatus>;
        /** 性能指标 */
        performance: Map<string, number>;
        /** 执行状态 */
        executionState: ExecutionState;
        /** 暂停原因 */
        pauseReason?: string;
    };

    /** 中间件间共享数据 */
    readonly shared: Map<string, any>;

    /** 响应队列 */
    readonly responses: ResponseItem[];

    /** 事件总线引用 */
    readonly eventBus: any; // MiddlewareEventBus类型，避免循环依赖

    /** 跳过后续中间件 */
    skip(reason?: string): void;

    /** 标记中间件执行失败 */
    fail(error: Error, middlewareName: string): void;

    /** 获取性能指标 */
    getPerformanceMetrics(): Record<string, number>;

    // === 执行控制方法 ===

    /** 暂停执行 */
    pause(reason?: string): void;

    /** 恢复执行 */
    resume(): void;

    /** 跳转到指定阶段或中间件 */
    jumpTo(target: { phase: MiddlewarePhase; middlewareId?: string }): void;

    /** 添加执行断点 */
    addBreakpoint(breakpoint: ExecutionBreakpoint): void;

    /** 移除执行断点 */
    removeBreakpoint(phase: MiddlewarePhase, middlewareId?: string): void;

    /** 检查是否应该暂停 */
    shouldPause(): boolean;

    // === 多响应支持方法 ===

    /** 添加响应到队列 */
    addResponse(type: ResponseType, content: string, metadata?: Record<string, any>): string;

    /** 获取所有响应 */
    getResponses(type?: ResponseType): ResponseItem[];

    /** 清空响应队列 */
    clearResponses(): void;

    /** 发送思考过程响应 */
    sendThinking(content: string, metadata?: Record<string, any>): Promise<void>;

    /** 发送中间结果响应 */
    sendIntermediate(content: string, metadata?: Record<string, any>): Promise<void>;

    /** 发送最终响应 */
    sendFinal(content: string, metadata?: Record<string, any>): Promise<void>;

    // === 事件通信方法 ===

    /** 发射事件 */
    emitEvent<T extends any>(event: T): Promise<void>;

    /** 订阅事件 */
    onEvent<T extends any>(eventType: string, handler: (event: T) => void | Promise<void>): void;

    /** 等待事件 */
    waitForEvent<T extends any>(eventType: string, filter?: (event: T) => boolean, timeout?: number): Promise<T>;

    /** 通知状态变更 */
    notifyStateChange(oldState: any, newState: any, context?: Record<string, any>): Promise<void>;

    /** 更新共享上下文并通知 */
    updateSharedContext(key: string, value: any, source: string): Promise<void>;
}

/**
 * 中间件接口
 */
export interface IMiddleware<TConfig = any> {
    /** 中间件唯一标识 */
    readonly id: string;
    /** 中间件名称 */
    readonly name: string;
    /** 执行阶段 */
    readonly phase: MiddlewarePhase;
    /** 优先级 */
    readonly priority: MiddlewarePriority;
    /** 是否启用 */
    readonly enabled: boolean;
    /** 依赖的中间件ID列表 */
    readonly dependencies: string[];
    /** 配置对象 */
    readonly config: TConfig;

    /** 执行中间件逻辑 */
    execute(ctx: MiddlewareContext, next: () => Promise<void>): Promise<void>;

    /** 初始化中间件 */
    initialize?(): Promise<void>;

    /** 清理资源 */
    dispose?(): Promise<void>;

    /** 健康检查 */
    healthCheck?(): Promise<boolean>;

    /** 获取中间件信息 */
    getInfo(): MiddlewareInfo;
}

/**
 * 中间件信息
 */
export interface MiddlewareInfo {
    id: string;
    name: string;
    phase: MiddlewarePhase;
    priority: number | MiddlewarePriority;
    enabled: boolean;
    dependencies: string[];
    version?: string;
    description?: string;
    author?: string;
}

/**
 * 中间件定义
 */
export interface MiddlewareDefinition<TConfig = any> {
    /** 中间件工厂函数 */
    factory: MiddlewareFactory<TConfig>;
    /** 默认配置 */
    defaultConfig?: TConfig;
    /** 配置验证函数 */
    validateConfig?: (config: TConfig) => boolean | string;
    /** 中间件元信息 */
    metadata: Omit<MiddlewareInfo, "enabled">;
}

/**
 * 中间件工厂函数
 */
export type MiddlewareFactory<TConfig = any> = (ctx: Context, config: TConfig) => IMiddleware<TConfig>;



